{"GlobalPropertiesHash": "h/FxxOc0tOt9Zt0rKePjLsiDFoES9YljsKy+9hPpDuI=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["+1prjCNk8EWcErIiF+SoEB5sRg/3W0fHu7GrOLaJra4=", "Pif6pieRU1dykxIoXZtUmS5qNC7MIlakAdBh0vEerLs=", "5cxVwFZEKU1AUFCtiWJl6GhxiZnNbUyAsE+KmCQaYk8=", "z55/O16866Anpdv5ELLgazBJ7TJ7FSCgdwSN6djTOjE=", "uqCAGZWAWS7Hr9W2dawOQ9mkCeZ3/k1F/3RJHfUpxv8=", "QPApLmQtPGqHokLL0cSBU7/Mhep5oKk9fN+BYfOh2gw=", "6RDpTmDwhf5scqg03dga55iPueu41PpGsw4AdozSCbI=", "OsCHUNXDYV/R+ROMmqjvXN3pSZI4eBls3uIaoese+Rc=", "S8zfqSv81TQU6+pmjZ1UxJ6osq33K/WcYth71CZm9ts=", "anVG7wSWsCMSu2AvSKvqNXSOP7Y7yf5Ndcy0cJh8ji4=", "Iix8EHcLdQ5JXbBAstgNh18HbEOPWeWiG1Q02mpZe6o=", "EsTD/H64ow2QDnA14fYIOh/Ou3ta39zEbY++gko1enc=", "wxMFjaLNE1gS7azw8ApJEy3GXQlhYJgddqKfg/ck9c4=", "nk3DrHH1nTvjxFRyiPpWSwX3swmTzpxUihZc5d+T+jk=", "li39R+t05JmIraPT2hcIXUIaZzuU4WqMjtVNfxtj7FI=", "Gp1A7aI5fLJyoSVBJQkyyDZyrybbwGqYPx9Fh6xZY44=", "3SA8n+eADdLT718UbkJmekavOBufLLl+DhntVwAFK3o=", "ivfBRqlzOMfGjGwU45e3aKdqsioyCdz4UOZKjBVa9cU=", "N85OWq5iffkeume8tAq0aDfXmpfbJh4Xs03qOJqoVqI=", "hpiBIMYZABSQF6cxkfwxLwH2PZV3+loK93rd+XazegM=", "hYQhIFPgveVt5TQ7booroxkzKxhqJEKi8YgqcMr0BA8=", "aIQCOEB4mlG3aqHXSCz2maXMk6GMTwTFWkC0w8F1G2Q=", "WF3mj0BV0ioar02YszmP2bk4NvMLkXB5Uew9sAfd9MQ=", "zwNkluGGvazm1YnmDE6MLccAMRjE6jvpMWGZrQ+esMg=", "zoZim2TnLLrWPxZQH1XDjt2/NlqdOYDSCgBlc2b/xs8=", "RR3e/6avW/z/6UiRqXsv9D56iSWWFyb8GlBDY81038I=", "XACO8HIZLRX46/aXOTJRit7Xf54wcioLVfAT/hmV23U=", "9j5hR17n4KSnBJnnVjXbAtTirAaccZFYSOasDv8sEh0=", "B79t6aIeJxDX+U/iQbw3ALU3EJTnuKf+Yj+jCmDHSRs=", "fQaMGuNDdXjZzU9V2EWOtP6CnnduyOprtNwgA4JO8ek=", "1DGkie7YNoBYAPbjY7j6XG03o9jgfcMmPHDuHmZUFAA=", "ajsTnjGk9iyN6bzaSymTctXddEfy8GIYHGWYt4uYTCA=", "P582MeLU1I98eW/QfjoGUVhmxYCJdC+ag83BmzLrtOI=", "Pv7QA8i8yI60+7LNrB7bo9VUF6SBG+3BQUH8JgiG4ZY=", "Blq+NAT+isJmZtAoylcrYPByUTTRN6oSQPMx7ReMAgU=", "ONIhOLwh7v9QV17evKpMsKXESnKsoK2G22bkKfzJ6e0=", "IvyTjRGO76ynXsCI90I+fAvIpxryspkWP3OK7AUBq7k=", "nZk9gazaUn73tPG0oziBaz9zxGp2mOE4N/8AmuDtZQk=", "i5pkxy79N99Sh/P2s6Nc6uSASrme3ikTBNItV32Uo4s=", "PZU2mi01GOfDpf+U/NFZ6C70eZC+7hSAozrMV4qdDiM=", "WKsjTE1hJKUXFiSKMb3fdgdpwDrlb8oLwPzV1lP1yMg=", "SGCgUzDoSfQwx6me349EgiBnDuQziPzi91SS5FRBQJg=", "AcpBRytofmzo9ubr8BvFB1LUAeUHhgiwF5U26mWs34I=", "/5Y44bWwp2eS+4ez0296e5XJKlzHCM9AMwgfl29PS6o=", "h+51loSQ0w73r5JVdvmtJM3Z3ZCUQm3A0eVEhG5YJtg=", "Pc0LZnyCkRXf5eGx7O/3HIwQDh1py311q3RAfts0JIw=", "TG2mZz3ZhBF/AOCboOJ/eM1pGPYk2mZrBOrP+BYJQPg=", "PeIQpbDyA+SVaz3LNmiXI/H1LJ0fbPCHSCrFZGBNMcI=", "3o3Atxab5nq5Uijq5HvMtb6dNZ/lc9+DTqcQ84wy3fE=", "+480Qpzj6+zghZFbB9P9PyAi1+/cgL4lEBuM/gubCsU=", "mVulXz5QRLZFiVm5uAaG0jFtcVolvHcMOikERRCcOwA=", "74521Gsr7LuvAOKz+ha3q/3GoMgfAvOl6sv9+xpCS7E=", "gpRLJl8jK5GWqybsKQs75DGqJaAP8bKqHL1AloMYcD8=", "4gl4ur3W7KOT237l6MFlBy+kKNEL99p04+/lfrS8HHg=", "KCsAsLhxkT1uQJAnHOGJHY0NZIMbf26FJB2a9N4nwcM=", "wjQBlJJa0aV2eMeqe3/eupzDMLzOj+7MlRjN8253xWQ=", "Ms+2KabN6YIDQINCFZyNuTNmN+J5psV2cHFlQb4T6Xw=", "Xc2XtW93i4qZqjsjUPb5z5qiF7bsVsuYaatuiqrJl8M=", "OYYWXFZoCM/gnrRvFC2LVrVXflX6PBbz/M6UOG1A6fo=", "bMZgAbS7+S3zW29ttPR95hEGvTFub+4yvmAvqdUXm7M=", "4rR1IqD4kD2iEDToXtk8KjPig016QsaXOATPt+Gd9Bk=", "iVq0VL03zL32C4NO483lwjSfSwDEUF9vMTK1F9Bd2ts=", "tr5idVLlf9OijXIvwsS8qroTfuj8RH3e6tOUyi/8/QE=", "986yekMeFtpi6kNWVFWPiGJIfpSU3j4hOiqRTcLzxto=", "OT8m9U9U2YaRc/Np76kDXCVsn1dyw1pu6GJxAdgKM20=", "BJxmUMpPVdezThQyiNMIyfLmbaTKOOeSyM6bFH7qeAQ=", "TRhlJOVQVdo3NWqatyVgcFBGFPgQ+3yw8xizCGUuQCA=", "HFGx662PDsKSTL4Hs6u0oNAmD+IIQUzFABoOGUj+KiA=", "QcXKRbuMkEbNGmwILQiW3MSuZwH29ag5OSXlY1KWI4Y=", "rEeagRxs76j37IAlOLKpXjKO8sTHTsCTa4tvxxWyALg="], "CachedAssets": {"+1prjCNk8EWcErIiF+SoEB5sRg/3W0fHu7GrOLaJra4=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\css\\site.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-05-23T07:18:49.6639648+00:00"}, "Pif6pieRU1dykxIoXZtUmS5qNC7MIlakAdBh0vEerLs=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\favicon.ico", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-05-23T07:18:49.7883996+00:00"}, "5cxVwFZEKU1AUFCtiWJl6GhxiZnNbUyAsE+KmCQaYk8=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\js\\site.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-05-23T07:18:49.6649627+00:00"}, "z55/O16866Anpdv5ELLgazBJ7TJ7FSCgdwSN6djTOjE=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-05-23T07:18:49.705134+00:00"}, "uqCAGZWAWS7Hr9W2dawOQ9mkCeZ3/k1F/3RJHfUpxv8=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-05-23T07:18:49.7061336+00:00"}, "QPApLmQtPGqHokLL0cSBU7/Mhep5oKk9fN+BYfOh2gw=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-05-23T07:18:49.7075958+00:00"}, "6RDpTmDwhf5scqg03dga55iPueu41PpGsw4AdozSCbI=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-05-23T07:18:49.709086+00:00"}, "OsCHUNXDYV/R+ROMmqjvXN3pSZI4eBls3uIaoese+Rc=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-05-23T07:18:49.7104225+00:00"}, "S8zfqSv81TQU6+pmjZ1UxJ6osq33K/WcYth71CZm9ts=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-05-23T07:18:49.7114371+00:00"}, "anVG7wSWsCMSu2AvSKvqNXSOP7Y7yf5Ndcy0cJh8ji4=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-05-23T07:18:49.712898+00:00"}, "Iix8EHcLdQ5JXbBAstgNh18HbEOPWeWiG1Q02mpZe6o=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-05-23T07:18:49.7139093+00:00"}, "EsTD/H64ow2QDnA14fYIOh/Ou3ta39zEbY++gko1enc=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-05-23T07:18:49.7139093+00:00"}, "wxMFjaLNE1gS7azw8ApJEy3GXQlhYJgddqKfg/ck9c4=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-05-23T07:18:49.7149173+00:00"}, "nk3DrHH1nTvjxFRyiPpWSwX3swmTzpxUihZc5d+T+jk=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-05-23T07:18:49.7159092+00:00"}, "li39R+t05JmIraPT2hcIXUIaZzuU4WqMjtVNfxtj7FI=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-05-23T07:18:49.7159092+00:00"}, "Gp1A7aI5fLJyoSVBJQkyyDZyrybbwGqYPx9Fh6xZY44=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-05-23T07:18:49.7173449+00:00"}, "3SA8n+eADdLT718UbkJmekavOBufLLl+DhntVwAFK3o=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-05-23T07:18:49.718796+00:00"}, "ivfBRqlzOMfGjGwU45e3aKdqsioyCdz4UOZKjBVa9cU=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-05-23T07:18:49.718796+00:00"}, "N85OWq5iffkeume8tAq0aDfXmpfbJh4Xs03qOJqoVqI=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-05-23T07:18:49.7202867+00:00"}, "hpiBIMYZABSQF6cxkfwxLwH2PZV3+loK93rd+XazegM=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-05-23T07:18:49.7212981+00:00"}, "hYQhIFPgveVt5TQ7booroxkzKxhqJEKi8YgqcMr0BA8=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-05-23T07:18:49.7222984+00:00"}, "aIQCOEB4mlG3aqHXSCz2maXMk6GMTwTFWkC0w8F1G2Q=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-05-23T07:18:49.7233002+00:00"}, "WF3mj0BV0ioar02YszmP2bk4NvMLkXB5Uew9sAfd9MQ=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-05-23T07:18:49.7247328+00:00"}, "zwNkluGGvazm1YnmDE6MLccAMRjE6jvpMWGZrQ+esMg=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-05-23T07:18:49.7259392+00:00"}, "zoZim2TnLLrWPxZQH1XDjt2/NlqdOYDSCgBlc2b/xs8=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-05-23T07:18:49.7272572+00:00"}, "RR3e/6avW/z/6UiRqXsv9D56iSWWFyb8GlBDY81038I=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-05-23T07:18:49.7287308+00:00"}, "XACO8HIZLRX46/aXOTJRit7Xf54wcioLVfAT/hmV23U=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-05-23T07:18:49.7297446+00:00"}, "9j5hR17n4KSnBJnnVjXbAtTirAaccZFYSOasDv8sEh0=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-05-23T07:18:49.7307412+00:00"}, "B79t6aIeJxDX+U/iQbw3ALU3EJTnuKf+Yj+jCmDHSRs=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-05-23T07:18:49.7340904+00:00"}, "fQaMGuNDdXjZzU9V2EWOtP6CnnduyOprtNwgA4JO8ek=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-05-23T07:18:49.7350862+00:00"}, "1DGkie7YNoBYAPbjY7j6XG03o9jgfcMmPHDuHmZUFAA=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-05-23T07:18:49.7380884+00:00"}, "ajsTnjGk9iyN6bzaSymTctXddEfy8GIYHGWYt4uYTCA=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-05-23T07:18:49.7400874+00:00"}, "P582MeLU1I98eW/QfjoGUVhmxYCJdC+ag83BmzLrtOI=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-05-23T07:18:49.7440895+00:00"}, "Pv7QA8i8yI60+7LNrB7bo9VUF6SBG+3BQUH8JgiG4ZY=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-05-23T07:18:49.7450876+00:00"}, "Blq+NAT+isJmZtAoylcrYPByUTTRN6oSQPMx7ReMAgU=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-05-23T07:18:49.7480904+00:00"}, "ONIhOLwh7v9QV17evKpMsKXESnKsoK2G22bkKfzJ6e0=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-05-23T07:18:49.7500911+00:00"}, "IvyTjRGO76ynXsCI90I+fAvIpxryspkWP3OK7AUBq7k=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-05-23T07:18:49.7540862+00:00"}, "nZk9gazaUn73tPG0oziBaz9zxGp2mOE4N/8AmuDtZQk=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-05-23T07:18:49.7561023+00:00"}, "i5pkxy79N99Sh/P2s6Nc6uSASrme3ikTBNItV32Uo4s=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-05-23T07:18:49.7591124+00:00"}, "PZU2mi01GOfDpf+U/NFZ6C70eZC+7hSAozrMV4qdDiM=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-05-23T07:18:49.7601127+00:00"}, "WKsjTE1hJKUXFiSKMb3fdgdpwDrlb8oLwPzV1lP1yMg=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-05-23T07:18:49.7631103+00:00"}, "SGCgUzDoSfQwx6me349EgiBnDuQziPzi91SS5FRBQJg=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-05-23T07:18:49.7641099+00:00"}, "AcpBRytofmzo9ubr8BvFB1LUAeUHhgiwF5U26mWs34I=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-05-23T07:18:49.771768+00:00"}, "/5Y44bWwp2eS+4ez0296e5XJKlzHCM9AMwgfl29PS6o=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-05-23T07:18:49.7731164+00:00"}, "h+51loSQ0w73r5JVdvmtJM3Z3ZCUQm3A0eVEhG5YJtg=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-05-23T07:18:49.7773912+00:00"}, "Pc0LZnyCkRXf5eGx7O/3HIwQDh1py311q3RAfts0JIw=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-05-23T07:18:49.7813987+00:00"}, "TG2mZz3ZhBF/AOCboOJ/eM1pGPYk2mZrBOrP+BYJQPg=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-05-23T07:18:49.7853995+00:00"}, "PeIQpbDyA+SVaz3LNmiXI/H1LJ0fbPCHSCrFZGBNMcI=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-23T07:18:49.780398+00:00"}, "3o3Atxab5nq5Uijq5HvMtb6dNZ/lc9+DTqcQ84wy3fE=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-05-23T07:18:49.7893989+00:00"}, "+480Qpzj6+zghZFbB9P9PyAi1+/cgL4lEBuM/gubCsU=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-05-23T07:18:49.7913978+00:00"}, "mVulXz5QRLZFiVm5uAaG0jFtcVolvHcMOikERRCcOwA=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-05-23T07:18:49.7913978+00:00"}, "74521Gsr7LuvAOKz+ha3q/3GoMgfAvOl6sv9+xpCS7E=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-05-23T07:18:49.6989241+00:00"}, "gpRLJl8jK5GWqybsKQs75DGqJaAP8bKqHL1AloMYcD8=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-05-23T07:18:49.6999239+00:00"}, "4gl4ur3W7KOT237l6MFlBy+kKNEL99p04+/lfrS8HHg=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-05-23T07:18:49.7009291+00:00"}, "KCsAsLhxkT1uQJAnHOGJHY0NZIMbf26FJB2a9N4nwcM=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-05-23T07:18:49.7029285+00:00"}, "wjQBlJJa0aV2eMeqe3/eupzDMLzOj+7MlRjN8253xWQ=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-23T07:18:49.7873995+00:00"}, "Ms+2KabN6YIDQINCFZyNuTNmN+J5psV2cHFlQb4T6Xw=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-05-23T07:18:49.6904039+00:00"}, "Xc2XtW93i4qZqjsjUPb5z5qiF7bsVsuYaatuiqrJl8M=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-05-23T07:18:49.6939258+00:00"}, "OYYWXFZoCM/gnrRvFC2LVrVXflX6PBbz/M6UOG1A6fo=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-05-23T07:18:49.6969282+00:00"}, "bMZgAbS7+S3zW29ttPR95hEGvTFub+4yvmAvqdUXm7M=": {"Identity": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "GDHealthcare.Web", "SourceType": "Discovered", "ContentRoot": "C:\\NEW HC\\Modular Monolithic + NEXTJS\\GDHealthcare\\src\\Presentation\\Web\\GDHealthcare.Web\\wwwroot\\", "BasePath": "_content/GDHealthcare.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-05-23T07:18:49.7843994+00:00"}}, "CachedCopyCandidates": {}}