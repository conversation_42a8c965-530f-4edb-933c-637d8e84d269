﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36109.1 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{8225598F-657B-4C96-9362-7DCC0CA6F1DB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{CF4F4B6F-6056-421F-AE41-619F4EAF45E1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{D59D4061-4777-41CD-80ED-95FAAEF3742C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{5FF3D535-A965-4749-A63A-AB35AD1C906A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Modules", "Modules", "{6CA119DB-4DEF-4B4B-AD38-07E9E86A54BF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Shared", "Shared", "{6129D5D7-9857-4582-AA18-2524015667FE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentation", "Presentation", "{E9537614-0209-45FC-B412-2475751F3F47}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Domain", "Domain", "{3186A460-2067-43B7-AAE2-66E58DF7A5D2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Application", "Application", "{6D4EB327-25A3-40AB-AFB7-BE71E79F9337}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{2C354DA0-C9FE-494E-9E0F-87208296B505}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Biomarkers", "Biomarkers", "{46B1E725-5904-46A7-A526-D82AB1B0A0C2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ClinicalTrials", "ClinicalTrials", "{8EA344AA-7EF4-40EF-986B-1E39844D91B1}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Companies", "Companies", "{B2EB8399-0A9B-40B9-B446-E894AFAF9E57}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Reports", "Reports", "{8FCBFB2E-7AEA-4826-890B-D2F9E3865D27}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Contracts", "Contracts", "{92AEEFF2-3BB6-4753-8BE2-4CE848770976}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{CB65094E-94BB-4E6F-A66E-E5ECBE37519E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{481F2AEE-4AA7-47C7-89F3-10604DE75F26}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{4E72CD29-E1AA-4970-922E-140423DDAE26}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "API", "API", "{463A7F5B-AF43-42E4-8507-F72B14D42BB6}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Frontend", "Frontend", "{170BD443-79BD-4439-9947-C541EEBB0513}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Core.Domain", "src\Core\Domain\GDHealthcare.Core.Domain\GDHealthcare.Core.Domain.csproj", "{10E4DACB-8A81-4670-87BA-4EA372A6F777}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Core.Application", "GDHealthcare\src\Core\Application\GDHealthcare.Core.Application\GDHealthcare.Core.Application.csproj", "{447EFD35-54ED-4DCC-A097-99C944EE6FE7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Core.Infrastructure", "GDHealthcare\src\Core\Infrastructure\GDHealthcare.Core.Infrastructure\GDHealthcare.Core.Infrastructure.csproj", "{F2762AAD-67F4-4090-A44B-B4410B3F3041}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Shared.Contracts", "GDHealthcare\src\Shared\Contracts\GDHealthcare.Shared.Contracts\GDHealthcare.Shared.Contracts.csproj", "{E7317D16-1BF2-4D5C-890D-DD16086B5046}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Shared.Common", "GDHealthcare\src\Shared\Common\GDHealthcare.Shared.Common\GDHealthcare.Shared.Common.csproj", "{A39ECEC4-7A1D-4D99-B94A-9A3E41D398D9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Shared.Infrastructure", "GDHealthcare\src\Shared\Infrastructure\GDHealthcare.Shared.Infrastructure\GDHealthcare.Shared.Infrastructure.csproj", "{FD160326-4B46-49DB-81A2-C2288CE7D5B7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Biomarkers.Application", "src\Modules\Biomarkers\GDHealthcare.Biomarkers.Application\GDHealthcare.Biomarkers.Application.csproj", "{A10C529D-24A8-49D6-92A1-0461678F14BF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Biomarkers.Domain", "src\Modules\Biomarkers\GDHealthcare.Biomarkers.Domain\GDHealthcare.Biomarkers.Domain.csproj", "{B118532C-3033-48A2-8D11-664E3CC78219}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Biomarkers.Infrastructure", "src\Modules\Biomarkers\GDHealthcare.Biomarkers.Infrastructure\GDHealthcare.Biomarkers.Infrastructure.csproj", "{3B166DC3-806F-4FD7-868B-5ED2FBB2B91E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Biomarkers.Presentation", "src\Modules\Biomarkers\GDHealthcare.Biomarkers.Presentation\GDHealthcare.Biomarkers.Presentation.csproj", "{DFFA36EA-01AB-41FD-94D8-33C6AA3105F5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.API", "src\Presentation\API\GDHealthcare.API\GDHealthcare.API.csproj", "{BEF54F93-6B9F-486F-A17C-05C741C001EA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "GDHealthcare.Web", "src\Presentation\Web\GDHealthcare.Web\GDHealthcare.Web.csproj", "{5D8B13CE-5BF9-4390-9A6D-2A89B231F98C}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{10E4DACB-8A81-4670-87BA-4EA372A6F777}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10E4DACB-8A81-4670-87BA-4EA372A6F777}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10E4DACB-8A81-4670-87BA-4EA372A6F777}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10E4DACB-8A81-4670-87BA-4EA372A6F777}.Release|Any CPU.Build.0 = Release|Any CPU
		{447EFD35-54ED-4DCC-A097-99C944EE6FE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{447EFD35-54ED-4DCC-A097-99C944EE6FE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{447EFD35-54ED-4DCC-A097-99C944EE6FE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{447EFD35-54ED-4DCC-A097-99C944EE6FE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{F2762AAD-67F4-4090-A44B-B4410B3F3041}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F2762AAD-67F4-4090-A44B-B4410B3F3041}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F2762AAD-67F4-4090-A44B-B4410B3F3041}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F2762AAD-67F4-4090-A44B-B4410B3F3041}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7317D16-1BF2-4D5C-890D-DD16086B5046}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7317D16-1BF2-4D5C-890D-DD16086B5046}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7317D16-1BF2-4D5C-890D-DD16086B5046}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7317D16-1BF2-4D5C-890D-DD16086B5046}.Release|Any CPU.Build.0 = Release|Any CPU
		{A39ECEC4-7A1D-4D99-B94A-9A3E41D398D9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A39ECEC4-7A1D-4D99-B94A-9A3E41D398D9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A39ECEC4-7A1D-4D99-B94A-9A3E41D398D9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A39ECEC4-7A1D-4D99-B94A-9A3E41D398D9}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD160326-4B46-49DB-81A2-C2288CE7D5B7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD160326-4B46-49DB-81A2-C2288CE7D5B7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD160326-4B46-49DB-81A2-C2288CE7D5B7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD160326-4B46-49DB-81A2-C2288CE7D5B7}.Release|Any CPU.Build.0 = Release|Any CPU
		{A10C529D-24A8-49D6-92A1-0461678F14BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A10C529D-24A8-49D6-92A1-0461678F14BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A10C529D-24A8-49D6-92A1-0461678F14BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A10C529D-24A8-49D6-92A1-0461678F14BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{B118532C-3033-48A2-8D11-664E3CC78219}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B118532C-3033-48A2-8D11-664E3CC78219}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B118532C-3033-48A2-8D11-664E3CC78219}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B118532C-3033-48A2-8D11-664E3CC78219}.Release|Any CPU.Build.0 = Release|Any CPU
		{3B166DC3-806F-4FD7-868B-5ED2FBB2B91E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3B166DC3-806F-4FD7-868B-5ED2FBB2B91E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3B166DC3-806F-4FD7-868B-5ED2FBB2B91E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3B166DC3-806F-4FD7-868B-5ED2FBB2B91E}.Release|Any CPU.Build.0 = Release|Any CPU
		{DFFA36EA-01AB-41FD-94D8-33C6AA3105F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DFFA36EA-01AB-41FD-94D8-33C6AA3105F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DFFA36EA-01AB-41FD-94D8-33C6AA3105F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DFFA36EA-01AB-41FD-94D8-33C6AA3105F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{BEF54F93-6B9F-486F-A17C-05C741C001EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BEF54F93-6B9F-486F-A17C-05C741C001EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BEF54F93-6B9F-486F-A17C-05C741C001EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BEF54F93-6B9F-486F-A17C-05C741C001EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D8B13CE-5BF9-4390-9A6D-2A89B231F98C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D8B13CE-5BF9-4390-9A6D-2A89B231F98C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D8B13CE-5BF9-4390-9A6D-2A89B231F98C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D8B13CE-5BF9-4390-9A6D-2A89B231F98C}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5FF3D535-A965-4749-A63A-AB35AD1C906A} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6CA119DB-4DEF-4B4B-AD38-07E9E86A54BF} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{6129D5D7-9857-4582-AA18-2524015667FE} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{E9537614-0209-45FC-B412-2475751F3F47} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{3186A460-2067-43B7-AAE2-66E58DF7A5D2} = {5FF3D535-A965-4749-A63A-AB35AD1C906A}
		{6D4EB327-25A3-40AB-AFB7-BE71E79F9337} = {5FF3D535-A965-4749-A63A-AB35AD1C906A}
		{2C354DA0-C9FE-494E-9E0F-87208296B505} = {5FF3D535-A965-4749-A63A-AB35AD1C906A}
		{46B1E725-5904-46A7-A526-D82AB1B0A0C2} = {6CA119DB-4DEF-4B4B-AD38-07E9E86A54BF}
		{8EA344AA-7EF4-40EF-986B-1E39844D91B1} = {6CA119DB-4DEF-4B4B-AD38-07E9E86A54BF}
		{B2EB8399-0A9B-40B9-B446-E894AFAF9E57} = {6CA119DB-4DEF-4B4B-AD38-07E9E86A54BF}
		{8FCBFB2E-7AEA-4826-890B-D2F9E3865D27} = {6CA119DB-4DEF-4B4B-AD38-07E9E86A54BF}
		{92AEEFF2-3BB6-4753-8BE2-4CE848770976} = {6129D5D7-9857-4582-AA18-2524015667FE}
		{CB65094E-94BB-4E6F-A66E-E5ECBE37519E} = {6129D5D7-9857-4582-AA18-2524015667FE}
		{481F2AEE-4AA7-47C7-89F3-10604DE75F26} = {6129D5D7-9857-4582-AA18-2524015667FE}
		{4E72CD29-E1AA-4970-922E-140423DDAE26} = {E9537614-0209-45FC-B412-2475751F3F47}
		{463A7F5B-AF43-42E4-8507-F72B14D42BB6} = {E9537614-0209-45FC-B412-2475751F3F47}
		{10E4DACB-8A81-4670-87BA-4EA372A6F777} = {3186A460-2067-43B7-AAE2-66E58DF7A5D2}
		{447EFD35-54ED-4DCC-A097-99C944EE6FE7} = {6D4EB327-25A3-40AB-AFB7-BE71E79F9337}
		{F2762AAD-67F4-4090-A44B-B4410B3F3041} = {2C354DA0-C9FE-494E-9E0F-87208296B505}
		{E7317D16-1BF2-4D5C-890D-DD16086B5046} = {92AEEFF2-3BB6-4753-8BE2-4CE848770976}
		{A39ECEC4-7A1D-4D99-B94A-9A3E41D398D9} = {CB65094E-94BB-4E6F-A66E-E5ECBE37519E}
		{FD160326-4B46-49DB-81A2-C2288CE7D5B7} = {481F2AEE-4AA7-47C7-89F3-10604DE75F26}
		{A10C529D-24A8-49D6-92A1-0461678F14BF} = {46B1E725-5904-46A7-A526-D82AB1B0A0C2}
		{B118532C-3033-48A2-8D11-664E3CC78219} = {46B1E725-5904-46A7-A526-D82AB1B0A0C2}
		{3B166DC3-806F-4FD7-868B-5ED2FBB2B91E} = {46B1E725-5904-46A7-A526-D82AB1B0A0C2}
		{DFFA36EA-01AB-41FD-94D8-33C6AA3105F5} = {46B1E725-5904-46A7-A526-D82AB1B0A0C2}
		{BEF54F93-6B9F-486F-A17C-05C741C001EA} = {463A7F5B-AF43-42E4-8507-F72B14D42BB6}
		{5D8B13CE-5BF9-4390-9A6D-2A89B231F98C} = {4E72CD29-E1AA-4970-922E-140423DDAE26}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {58995F67-067D-470B-87BE-879E08CDA0A7}
	EndGlobalSection
EndGlobal
